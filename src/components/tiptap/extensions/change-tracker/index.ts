import { <PERSON>lugin, Plugin<PERSON><PERSON> } from "@tiptap/pm/state";
import { Decoration, DecorationSet } from "@tiptap/pm/view";
import { Extension, Mark, mergeAttributes } from "@tiptap/core";
import { Step, StepMap, Transform } from "@tiptap/pm/transform";
import { Fragment } from "@tiptap/pm/model";

// Type definitions for step properties
interface ReplaceStep extends Step {
  jsonID: string;
  from: number;
  to: number;
  slice: {
    content: Fragment;
  };
}

interface NodeLike {
  textContent?: string;
  text?: string;
}

// Constants
const MARK_INSERTION = "insertion";
const MARK_DELETION = "deletion";
const EXTENSION_NAME = "trackchange";

// Data structures for tracking changes
class Span {
  constructor(
    public from: number,
    public to: number,
    public commit: number | null
  ) {}
}

class Commit {
  constructor(
    public message: string,
    public time: Date,
    public steps: Step[],
    public maps: StepMap[],
    public hidden?: boolean,
    public deletions?: Array<{ pos: number; content: string }>,
    public snapshot?: string, // Full document HTML snapshot
    public isSnapshotCommit?: boolean // Whether this commit has a snapshot
  ) {}
}

class TrackState {
  constructor(
    public blameMap: Span[],
    public commits: Commit[],
    public uncommittedSteps: Step[],
    public uncommittedMaps: StepMap[],
    public snapshotInterval: number = 10, // Create snapshot every N commits
    public baseDocumentSnapshot?: string // Initial document state
  ) {}

  // Apply a transform to this state
  applyTransform(transform: Transform): TrackState {
    // Invert the steps in the transaction, to be able to save them in the next commit
    const inverted = transform.steps.map((step, i) =>
      step.invert(transform.docs[i])
    );
    const newBlame = updateBlameMap(
      this.blameMap,
      transform,
      this.commits.length
    );
    // Create a new state—since these are part of the editor state, a
    // persistent data structure, they must not be mutated.
    return new TrackState(
      newBlame,
      this.commits,
      this.uncommittedSteps.concat(inverted),
      this.uncommittedMaps.concat(transform.mapping.maps)
    );
  }

  // When a transaction is marked as a commit, this is used to put any
  // uncommitted steps into a new commit.
  applyCommit(
    message: string,
    time: Date,
    documentSnapshot?: string
  ): TrackState {
    if (this.uncommittedSteps.length == 0) return this;

    // Extract deletion information from the steps
    const deletions: Array<{ pos: number; content: string }> = [];
    this.uncommittedSteps.forEach((step) => {
      // Type guard to check if step has replace properties
      const stepWithProps = step as ReplaceStep;
      if (
        stepWithProps.jsonID === "replace" &&
        stepWithProps.slice &&
        stepWithProps.slice.content
      ) {
        // This step represents a replacement that includes deletions
        const deletedContent = stepWithProps.slice.content;
        if (deletedContent.size > 0) {
          // Extract text content from the deleted slice
          let textContent = "";
          deletedContent.forEach((node: NodeLike) => {
            if (node.textContent) {
              textContent += node.textContent;
            } else if (node.text) {
              textContent += node.text;
            }
          });
          if (textContent) {
            deletions.push({
              pos: stepWithProps.from,
              content: textContent,
            });
          }
        }
      }
    });

    // Determine if this should be a snapshot commit
    const newCommitIndex = this.commits.length;
    const shouldSnapshot =
      newCommitIndex % this.snapshotInterval === 0 || newCommitIndex === 0;

    const commit = new Commit(
      message,
      time,
      this.uncommittedSteps,
      this.uncommittedMaps,
      false,
      deletions,
      shouldSnapshot ? documentSnapshot : undefined,
      shouldSnapshot
    );

    return new TrackState(
      this.blameMap,
      this.commits.concat(commit),
      [],
      [],
      this.snapshotInterval,
      this.baseDocumentSnapshot
    );
  }
}

function updateBlameMap(map: Span[], transform: Transform, id: number): Span[] {
  const result: Span[] = [];
  const mapping = transform.mapping;
  for (let i = 0; i < map.length; i++) {
    const span = map[i];
    const from = mapping.map(span.from, 1),
      to = mapping.map(span.to, -1);
    if (from < to) result.push(new Span(from, to, span.commit));
  }

  for (let i = 0; i < mapping.maps.length; i++) {
    const map = mapping.maps[i],
      after = mapping.slice(i + 1);
    map.forEach((_s: number, _e: number, start: number, end: number) => {
      insertIntoBlameMap(result, after.map(start, 1), after.map(end, -1), id);
    });
  }

  return result;
}

function insertIntoBlameMap(
  map: Span[],
  from: number,
  to: number,
  commit: number
): void {
  if (from >= to) return;
  let pos = 0;
  let next: Span;
  for (; pos < map.length; pos++) {
    next = map[pos];
    if (next.commit == commit) {
      if (next.to >= from) break;
    } else if (next.to > from) {
      // Different commit, not before
      if (next.from < from) {
        // Sticks out to the left (loop below will handle right side)
        const left = new Span(next.from, from, next.commit);
        if (next.to > to) map.splice(pos++, 0, left);
        else map[pos++] = left;
      }
      break;
    }
  }

  while ((next = map[pos])) {
    if (next.commit == commit) {
      if (next.from > to) break;
      from = Math.min(from, next.from);
      to = Math.max(to, next.to);
      map.splice(pos, 1);
    } else {
      if (next.from >= to) break;
      if (next.to > to) {
        map[pos] = new Span(to, next.to, next.commit);
        break;
      } else {
        map.splice(pos, 1);
      }
    }
  }

  map.splice(pos, 0, new Span(from, to, commit));
}

// Document reconstruction functions for hybrid approach
function findNearestSnapshot(
  trackState: TrackState,
  targetCommitIndex: number
): {
  snapshotIndex: number;
  snapshot: string;
} {
  // Look backwards from target commit to find the nearest snapshot
  for (let i = targetCommitIndex; i >= 0; i--) {
    const commit = trackState.commits[i];
    if (commit.isSnapshotCommit && commit.snapshot) {
      return { snapshotIndex: i, snapshot: commit.snapshot };
    }
  }

  // If no snapshot found, use base document
  return {
    snapshotIndex: -1,
    snapshot: trackState.baseDocumentSnapshot || "<p></p>",
  };
}

function reconstructDocumentAtCommit(
  trackState: TrackState,
  targetCommitIndex: number,
  baseDoc: any // ProseMirror document
): string {
  // If target is beyond available commits, return current state
  if (targetCommitIndex >= trackState.commits.length) {
    return ""; // Will be handled by caller
  }

  // Find the nearest snapshot
  const { snapshotIndex, snapshot } = findNearestSnapshot(
    trackState,
    targetCommitIndex
  );

  // If we found a snapshot at exactly the target commit, return it
  if (snapshotIndex === targetCommitIndex) {
    return snapshot;
  }

  // If we need to apply steps from snapshot to target
  if (snapshotIndex < targetCommitIndex) {
    // For now, return the snapshot - full step application would require
    // more complex ProseMirror document manipulation
    // TODO: Implement step-by-step reconstruction
    return snapshot;
  }

  // If target is before the nearest snapshot, we need to reconstruct from base
  // This is a simplified version - in production, you'd want full step application
  return snapshot;
}

// Plugin key for the track changes plugin
const trackPluginKey = new PluginKey("trackChanges");

// Main tracking plugin with proper batching
const trackPlugin = new Plugin({
  key: trackPluginKey,
  state: {
    init(_, instance) {
      // Store the initial document as base snapshot
      const baseSnapshot = instance.doc.toString(); // or use a serializer for HTML
      return new TrackState(
        [new Span(0, instance.doc.content.size, null)],
        [],
        [],
        [],
        10, // snapshot every 10 commits
        baseSnapshot
      );
    },
    apply(tr, tracked) {
      // Only track changes when explicitly committing via Ctrl+S or save button
      const commitMessage = tr.getMeta(trackPluginKey);
      const documentSnapshot = tr.getMeta("documentSnapshot");

      if (commitMessage) {
        // Commit all accumulated changes at once
        tracked = tracked.applyCommit(
          commitMessage,
          new Date(tr.time),
          documentSnapshot
        );
      } else if (tr.docChanged) {
        // Accumulate changes but don't create individual steps for each keystroke
        // This batches all changes until a commit is made
        tracked = tracked.applyTransform(tr);
      }
      return tracked;
    },
  },
});

// Plugin for highlighting changes
const highlightPluginKey = new PluginKey("highlightChanges");

// Helper function to calculate diff between two commits
function calculateCommitDiff(
  trackState: TrackState,
  currentCommit: Commit
): Decoration[] {
  const commits = trackState.commits;
  const currentIndex = commits.indexOf(currentCommit);

  if (currentIndex === -1) return [];

  const decorations: Decoration[] = [];

  // Show only additions for the selected commit (content that belongs to this commit only)
  const currentSpans = trackState.blameMap.filter(
    (span) => span.commit === currentIndex
  );

  // Show additions (content that belongs to current commit)
  currentSpans.forEach((span) => {
    decorations.push(
      Decoration.inline(span.from, span.to, {
        class: "commit-diff-addition",
        "data-commit-message": currentCommit.message,
        "data-commit-time": currentCommit.time.toISOString(),
      })
    );
  });

  // Show deletions for this commit
  if (currentCommit.deletions && currentCommit.deletions.length > 0) {
    currentCommit.deletions.forEach((deletion) => {
      decorations.push(
        Decoration.widget(deletion.pos, () => {
          const span = document.createElement("span");
          span.className = "commit-diff-deletion";
          span.setAttribute("data-commit-message", currentCommit.message);
          span.setAttribute(
            "data-commit-time",
            currentCommit.time.toISOString()
          );
          span.textContent = deletion.content;
          span.title = `Deleted: "${deletion.content}"`;
          return span;
        })
      );
    });
  }

  return decorations;
}

const highlightPlugin = new Plugin({
  key: highlightPluginKey,
  state: {
    init() {
      return { deco: DecorationSet.empty, commit: null };
    },
    apply(tr, prev, oldState, state) {
      const highlight = tr.getMeta(highlightPluginKey);
      if (highlight && highlight.add != null && prev.commit != highlight.add) {
        const tState = trackPlugin.getState(oldState);
        const decos = calculateCommitDiff(tState, highlight.add);
        return {
          deco: DecorationSet.create(state.doc, decos),
          commit: highlight.add,
        };
      } else if (
        highlight &&
        (highlight.clear === true || highlight.clear != null)
      ) {
        return { deco: DecorationSet.empty, commit: null };
      } else if (tr.docChanged && prev.commit) {
        return { deco: prev.deco.map(tr.mapping, tr.doc), commit: prev.commit };
      } else {
        return prev;
      }
    },
  },
  props: {
    decorations(state) {
      return this.getState(state).deco;
    },
  },
});

// Insertion mark for tracking additions
export const InsertionMark = Mark.create({
  name: MARK_INSERTION,
  addAttributes() {
    return {
      "data-user-id": {
        type: "string",
        default: "",
      },
      "data-user-name": {
        type: "string",
        default: "",
      },
      "data-timestamp": {
        type: "string",
        default: "",
      },
    };
  },
  parseHTML() {
    return [{ tag: "ins" }];
  },
  renderHTML({ HTMLAttributes }) {
    return [
      "ins",
      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, {
        style: "background-color: #d4edda; text-decoration: none;",
      }),
      0,
    ];
  },
});

// Deletion mark for tracking deletions
export const DeletionMark = Mark.create({
  name: MARK_DELETION,
  addAttributes() {
    return {
      "data-user-id": {
        type: "string",
        default: "",
      },
      "data-user-name": {
        type: "string",
        default: "",
      },
      "data-timestamp": {
        type: "string",
        default: "",
      },
    };
  },
  parseHTML() {
    return [{ tag: "del" }];
  },
  renderHTML({ HTMLAttributes }) {
    return [
      "del",
      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, {
        style: "background-color: #f8d7da; text-decoration: line-through;",
      }),
      0,
    ];
  },
});

declare module "@tiptap/core" {
  interface Commands<ReturnType> {
    trackchange: {
      /**
       * Commit current changes with a message
       */
      commitChanges: (message: string) => ReturnType;
      /**
       * Check if there are uncommitted changes
       */
      hasUncommittedChanges: () => ReturnType;
      /**
       * Get the current track state
       */
      getTrackState: () => ReturnType;
      /**
       * Highlight a specific commit
       */
      highlightCommit: (commit: Commit | null) => ReturnType;
      /**
       * Clear all highlights
       */
      clearAllHighlights: () => ReturnType;
      /**
       * Restore latest content without highlights
       */
      restoreLatestContent: () => ReturnType;
      /**
       * Save document (alias for commitChanges)
       */
      saveDocument: (message?: string) => ReturnType;
      /**
       * View document at a specific commit
       */
      viewDocumentAtCommit: (commitIndex: number) => ReturnType;
      /**
       * Return to the latest version
       */
      returnToLatestVersion: () => ReturnType;
      /**
       * Get document HTML at specific commit
       */
      getDocumentAtCommit: (commitIndex: number) => ReturnType;
    };
  }
}

// Main extension
export const TrackChangeExtension = Extension.create<{
  enabled: boolean;
  userId?: string;
  userName?: string;
}>({
  name: EXTENSION_NAME,

  addOptions() {
    return {
      enabled: true,
      userId: "",
      userName: "",
    };
  },

  addExtensions() {
    return [InsertionMark, DeletionMark];
  },

  addProseMirrorPlugins() {
    return [trackPlugin, highlightPlugin];
  },

  addCommands() {
    return {
      commitChanges:
        (message: string) =>
        ({ tr, dispatch }) => {
          if (dispatch) {
            tr.setMeta(trackPluginKey, message);
            dispatch(tr);
          }
          return true;
        },

      hasUncommittedChanges:
        () =>
        ({ state }) => {
          const trackState = trackPlugin.getState(state);
          return trackState?.uncommittedSteps.length > 0;
        },

      getTrackState:
        () =>
        ({ state, editor }) => {
          const trackState = trackPlugin.getState(state);
          // Store the track state in the editor's storage for external access
          if (editor && trackState) {
            editor.storage.trackchange.currentTrackState = trackState;
          }
          return true;
        },

      highlightCommit:
        (commit: Commit | null) =>
        ({ tr, dispatch }) => {
          if (dispatch) {
            if (commit) {
              tr.setMeta(highlightPluginKey, { add: commit });
            } else {
              tr.setMeta(highlightPluginKey, { clear: true });
            }
            dispatch(tr);
          }
          return true;
        },

      clearAllHighlights:
        () =>
        ({ tr, dispatch }) => {
          if (dispatch) {
            tr.setMeta(highlightPluginKey, { clear: true });
            dispatch(tr);
          }
          return true;
        },

      restoreLatestContent:
        () =>
        ({ tr, dispatch }) => {
          if (dispatch) {
            // Clear highlighting and force a clean state
            tr.setMeta(highlightPluginKey, { clear: true });
            dispatch(tr);
          }
          return true;
        },

      saveDocument:
        (message?: string) =>
        ({ tr, dispatch, state, editor }) => {
          const trackState = trackPlugin.getState(state);
          if (trackState?.uncommittedSteps.length > 0) {
            if (dispatch) {
              // Get current document HTML for snapshot if needed
              const documentSnapshot = editor?.getHTML();
              tr.setMeta(trackPluginKey, message || "Document saved");
              tr.setMeta("documentSnapshot", documentSnapshot);
              dispatch(tr);
            }
            return true;
          }
          return false;
        },

      viewDocumentAtCommit:
        (commitIndex: number) =>
        ({ tr, dispatch, state, editor }) => {
          const trackState = trackPlugin.getState(state);
          if (
            !trackState ||
            commitIndex < 0 ||
            commitIndex >= trackState.commits.length
          ) {
            return false;
          }

          if (dispatch && editor) {
            // Get the document HTML at the specified commit
            const documentHTML = reconstructDocumentAtCommit(
              trackState,
              commitIndex,
              state.doc
            );

            if (documentHTML) {
              // Store current viewing state
              tr.setMeta("viewingCommit", commitIndex);
              tr.setMeta("viewingDocumentHTML", documentHTML);
              dispatch(tr);

              // Set the editor content to the reconstructed document
              // Note: This is a simplified approach - in production you'd want
              // to handle this more carefully to preserve editor state
              setTimeout(() => {
                editor.commands.setContent(documentHTML);
              }, 0);
            }
          }
          return true;
        },

      returnToLatestVersion:
        () =>
        ({ tr, dispatch, state, editor }) => {
          const trackState = trackPlugin.getState(state);
          if (!trackState || !editor) {
            return false;
          }

          if (dispatch) {
            // Clear viewing state
            tr.setMeta("viewingCommit", null);
            tr.setMeta("viewingDocumentHTML", null);
            dispatch(tr);

            // Reconstruct the latest version (all commits applied)
            const latestCommitIndex = trackState.commits.length - 1;
            if (latestCommitIndex >= 0) {
              const latestHTML = reconstructDocumentAtCommit(
                trackState,
                latestCommitIndex,
                state.doc
              );
              if (latestHTML) {
                setTimeout(() => {
                  editor.commands.setContent(latestHTML);
                }, 0);
              }
            }
          }
          return true;
        },

      getDocumentAtCommit:
        (commitIndex: number) =>
        ({ state, editor }) => {
          const trackState = trackPlugin.getState(state);
          if (
            !trackState ||
            commitIndex < 0 ||
            commitIndex >= trackState.commits.length
          ) {
            return false;
          }

          // Store the result in editor storage for external access
          if (editor && trackState) {
            const documentHTML = reconstructDocumentAtCommit(
              trackState,
              commitIndex,
              state.doc
            );
            editor.storage.trackchange.documentAtCommit = {
              commitIndex,
              html: documentHTML,
              commit: trackState.commits[commitIndex],
            };
          }
          return true;
        },
    };
  },

  addStorage() {
    return {
      hasUncommittedChanges: false,
      currentTrackState: null,
      documentAtCommit: null,
      viewingCommit: null,
      isViewingHistoricalVersion: false,
    };
  },

  onTransaction({ editor }) {
    // Update storage to track if there are uncommitted changes
    const trackState = trackPlugin.getState(editor.state);
    this.storage.hasUncommittedChanges =
      trackState?.uncommittedSteps.length > 0;
  },
});

// Export types and classes for external use
export { Commit, TrackState, Span };

// Export utility functions
export * from "./utils";

export default TrackChangeExtension;
