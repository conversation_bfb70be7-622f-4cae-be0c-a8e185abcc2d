"use client";

import React, { useState, useEffect } from "react";
import { Editor } from "@tiptap/core";
import { formatTimestamp, saveDocument, hasUncommittedChanges } from "./utils";

interface Commit {
  message: string;
  time: Date;
  steps: any[];
  maps: any[];
  hidden?: boolean;
}

interface HistoryPanelProps {
  editor: Editor | null;
  isOpen: boolean;
  onClose: () => void;
}

export const HistoryPanel: React.FC<HistoryPanelProps> = ({
  editor,
  isOpen,
  onClose,
}) => {
  const [commits, setCommits] = useState<Commit[]>([]);
  const [hasUnsaved, setHasUnsaved] = useState(false);
  const [commitMessage, setCommitMessage] = useState("");
  const [viewingCommit, setViewingCommit] = useState<number | null>(null);
  const [isViewingHistorical, setIsViewingHistorical] = useState(false);

  // Update commits and unsaved status
  useEffect(() => {
    if (!editor) return;

    const updateState = () => {
      // Get the actual track state from editor storage
      editor.commands.getTrackState();
      const trackState = editor.storage.trackchange?.currentTrackState;

      if (trackState) {
        setCommits(trackState.commits || []);
      } else {
        setCommits([]);
      }

      setHasUnsaved(hasUncommittedChanges(editor));
      setViewingCommit(editor.storage.trackchange?.viewingCommit || null);
      setIsViewingHistorical(
        editor.storage.trackchange?.isViewingHistoricalVersion || false
      );
    };

    // Update on editor transactions
    editor.on("transaction", updateState);
    updateState();

    return () => {
      editor.off("transaction", updateState);
    };
  }, [editor]);

  const handleSave = () => {
    if (!editor) return;

    const message =
      commitMessage.trim() || `Saved at ${new Date().toLocaleTimeString()}`;
    const success = saveDocument(editor, message);

    if (success) {
      setCommitMessage("");
    }
  };

  const handleCommitHover = (commit: Commit) => {
    if (!editor || isViewingHistorical) return;
    editor.commands.highlightCommit(commit);
  };

  const handleCommitLeave = () => {
    if (!editor || isViewingHistorical) return;
    editor.commands.highlightCommit(null);
  };

  const handleCommitClick = (commitIndex: number) => {
    if (!editor) return;

    // View the document at this specific commit
    editor.commands.viewDocumentAtCommit(commitIndex);
    setViewingCommit(commitIndex);
    setIsViewingHistorical(true);
  };

  const handleReturnToLatest = () => {
    if (!editor) return;

    // Return to the latest version
    editor.commands.returnToLatestVersion();
    setViewingCommit(null);
    setIsViewingHistorical(false);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed right-0 top-0 h-full w-80 bg-white border-l border-gray-200 shadow-lg z-50 flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900">
          Document History
        </h2>
        <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
          <svg
            className="w-6 h-6"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>

      {/* Version Indicator */}
      {isViewingHistorical && (
        <div className="p-3 bg-blue-50 border-b border-blue-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 rounded-full bg-blue-500" />
              <span className="text-sm text-blue-800 font-medium">
                Viewing Version{" "}
                {viewingCommit !== null ? viewingCommit + 1 : "?"} of{" "}
                {commits.length}
              </span>
            </div>
            <button
              onClick={handleReturnToLatest}
              className="text-xs text-blue-600 hover:text-blue-800 underline"
            >
              Return to Latest
            </button>
          </div>
          <p className="text-xs text-blue-600 mt-1">
            You're viewing a historical version. Changes are read-only.
          </p>
        </div>
      )}

      {/* Save Section */}
      <div className="p-4 border-b border-gray-200">
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <div
              className={`w-2 h-2 rounded-full ${hasUnsaved ? "bg-yellow-500" : "bg-green-500"}`}
            />
            <span className="text-sm text-gray-600">
              {hasUnsaved ? "Unsaved changes" : "All changes saved"}
            </span>
          </div>

          {hasUnsaved && (
            <div className="space-y-2">
              <input
                type="text"
                value={commitMessage}
                onChange={(e) => setCommitMessage(e.target.value)}
                placeholder="Describe your changes..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    handleSave();
                  }
                }}
              />
              <button
                onClick={handleSave}
                className="w-full px-3 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                Save Changes
              </button>
            </div>
          )}
        </div>
      </div>

      {/* History List */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-4">
          <h3 className="text-sm font-medium text-gray-900 mb-3">
            Recent Changes
          </h3>

          {commits.length === 0 ? (
            <div className="text-center py-8">
              <div className="text-gray-400 mb-2">
                <svg
                  className="w-12 h-12 mx-auto"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1}
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
              </div>
              <p className="text-sm text-gray-500">No changes saved yet</p>
              <p className="text-xs text-gray-400 mt-1">
                Start editing to see your document history
              </p>
            </div>
          ) : (
            <div className="space-y-2">
              {commits.map((commit, index) => {
                const isCurrentlyViewing = viewingCommit === index;
                const isLatestCommit = index === commits.length - 1;

                return (
                  <div
                    key={index}
                    className={`p-3 border rounded-md cursor-pointer transition-colors ${
                      isCurrentlyViewing
                        ? "border-blue-300 bg-blue-50"
                        : "border-gray-200 hover:bg-gray-50"
                    }`}
                    onMouseEnter={() => handleCommitHover(commit)}
                    onMouseLeave={handleCommitLeave}
                    onClick={() => handleCommitClick(index)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {commit.message}
                          </p>
                          {isLatestCommit && !isViewingHistorical && (
                            <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                              Latest
                            </span>
                          )}
                          {isCurrentlyViewing && (
                            <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                              Viewing
                            </span>
                          )}
                          {commit.isSnapshotCommit && (
                            <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
                              📸 Snapshot
                            </span>
                          )}
                        </div>
                        <p className="text-xs text-gray-500 mt-1">
                          {formatTimestamp(commit.time)} • Version {index + 1}
                        </p>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200 bg-gray-50">
        <p className="text-xs text-gray-500 text-center">
          Press Ctrl+S to save changes quickly
        </p>
      </div>
    </div>
  );
};
