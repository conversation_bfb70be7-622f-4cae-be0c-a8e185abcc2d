"use client";

import React, { useState } from "react";
import { useEditor, EditorContent } from "@tiptap/react";
import { StarterKit } from "@tiptap/starter-kit";
import { TrackChangeExtension } from "./index";
import { HistoryPanel } from "./HistoryPanel";
import {
  addSaveShortcut,
  getCommitStats,
  isViewingHistoricalVersion,
  getCurrentViewingCommit,
  exportCommitHistory,
} from "./utils";

export const HybridTrackingExample: React.FC = () => {
  const [isHistoryOpen, setIsHistoryOpen] = useState(false);
  const [stats, setStats] = useState<any>(null);

  const editor = useEditor({
    extensions: [
      StarterKit,
      TrackChangeExtension.configure({
        enabled: true,
        userId: "demo-user",
        userName: "Demo User",
      }),
    ],
    content: `
      <h1>Hybrid Change Tracking Demo</h1>
      <p>This document demonstrates the hybrid approach to change tracking:</p>
      <ul>
        <li><strong>Incremental Changes:</strong> Each edit is tracked as steps</li>
        <li><strong>Periodic Snapshots:</strong> Full document snapshots every 10 commits</li>
        <li><strong>Version Navigation:</strong> Click on any version in history to view it</li>
        <li><strong>True Version History:</strong> See exactly what the document looked like at each save</li>
      </ul>
      <p>Try editing this document and saving with Ctrl+S to see the version tracking in action!</p>
    `,
    onTransaction: () => {
      // Update stats when editor changes
      if (editor) {
        setStats(getCommitStats(editor));
      }
    },
  });

  React.useEffect(() => {
    if (!editor) return;

    // Add keyboard shortcut for saving
    const cleanup = addSaveShortcut(editor);

    // Initial stats
    setStats(getCommitStats(editor));

    return cleanup;
  }, [editor]);

  const handleExportHistory = () => {
    if (!editor) return;

    const history = exportCommitHistory(editor);
    if (history) {
      // Download as JSON file
      const blob = new Blob([JSON.stringify(history, null, 2)], {
        type: "application/json",
      });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `document-history-${new Date().toISOString().split("T")[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  const isViewingHistorical = editor
    ? isViewingHistoricalVersion(editor)
    : false;
  const currentViewingCommit = editor ? getCurrentViewingCommit(editor) : null;

  if (!editor) {
    return <div>Loading editor...</div>;
  }

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Main Editor Area */}
      <div className="flex-1 flex flex-col">
        {/* Toolbar */}
        <div className="bg-white border-b border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h1 className="text-lg font-semibold text-gray-900">
                Hybrid Change Tracking Demo
              </h1>

              {/* Version Status */}
              {isViewingHistorical && (
                <div className="flex items-center space-x-2 px-3 py-1 bg-blue-100 rounded-full">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-sm text-blue-800 font-medium">
                    Viewing Version{" "}
                    {currentViewingCommit !== null
                      ? currentViewingCommit + 1
                      : "?"}
                  </span>
                </div>
              )}
            </div>

            <div className="flex items-center space-x-3">
              {/* Stats Display */}
              {stats && (
                <div className="text-sm text-gray-600">
                  <span className="font-medium">{stats.totalCommits}</span>{" "}
                  versions
                  {stats.snapshotCommits > 0 && (
                    <span className="ml-2">
                      •{" "}
                      <span className="font-medium">
                        {stats.snapshotCommits}
                      </span>{" "}
                      snapshots
                    </span>
                  )}
                  {stats.hasUnsavedChanges && (
                    <span className="ml-2 text-yellow-600">
                      • Unsaved changes
                    </span>
                  )}
                </div>
              )}

              {/* Action Buttons */}
              <button
                onClick={handleExportHistory}
                className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
                disabled={!stats || stats.totalCommits === 0}
              >
                Export History
              </button>

              <button
                onClick={() => setIsHistoryOpen(!isHistoryOpen)}
                className="px-3 py-1 text-sm bg-blue-600 text-white hover:bg-blue-700 rounded-md transition-colors"
              >
                {isHistoryOpen ? "Hide" : "Show"} History
              </button>
            </div>
          </div>
        </div>

        {/* Editor Content */}
        <div className="flex-1 p-6 overflow-auto">
          <div className="max-w-4xl mx-auto">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <EditorContent
                editor={editor}
                className="prose prose-lg max-w-none focus:outline-none"
              />
            </div>
          </div>
        </div>

        {/* Instructions */}
        <div className="bg-gray-100 border-t border-gray-200 p-4">
          <div className="max-w-4xl mx-auto">
            <div className="flex items-center justify-between text-sm text-gray-600">
              <div className="flex items-center space-x-6">
                <span>
                  <kbd className="px-2 py-1 bg-gray-200 rounded text-xs">
                    Ctrl+S
                  </kbd>{" "}
                  Save changes
                </span>
                <span>Click versions in history to view them</span>
                <span>Snapshots are created every 10 versions</span>
              </div>
              <div className="text-xs text-gray-500">
                Hybrid Change Tracking • Incremental + Snapshots
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* History Panel */}
      <HistoryPanel
        editor={editor}
        isOpen={isHistoryOpen}
        onClose={() => setIsHistoryOpen(false)}
      />
    </div>
  );
};

export default HybridTrackingExample;
