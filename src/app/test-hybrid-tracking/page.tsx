"use client";

import React from 'react';
import { Editor } from '@/components/tiptap/editor';
import { Toolbar } from '@/components/tiptap/toolbar';

export default function TestHybridTrackingPage() {
  const initialContent = `
    <h1>Hybrid Change Tracking Test</h1>
    <p>This document demonstrates the new hybrid approach to change tracking:</p>
    <ul>
      <li><strong>Incremental Changes:</strong> Each edit is tracked as steps</li>
      <li><strong>Periodic Snapshots:</strong> Full document snapshots every 10 commits</li>
      <li><strong>Version Navigation:</strong> Click on any version in history to view it</li>
      <li><strong>True Version History:</strong> See exactly what the document looked like at each save</li>
    </ul>
    <p>Try editing this document and saving with Ctrl+S to see the version tracking in action!</p>
    
    <h2>How to Test:</h2>
    <ol>
      <li>Make some edits to this text</li>
      <li>Press Ctrl+S or click the Save button to create a commit</li>
      <li>Make more edits and save again</li>
      <li>Click the Git Commit icon in the toolbar to see version history</li>
      <li>Click on any version to view that exact document state</li>
      <li>Use the History Panel button to open the full history panel</li>
    </ol>
    
    <h2>Features to Notice:</h2>
    <ul>
      <li>📸 Snapshot commits (every 10th commit) are marked with a camera icon</li>
      <li>🔄 Version navigation shows exactly what the document looked like at each save</li>
      <li>⚡ Performance is optimized with smart snapshots</li>
      <li>🎯 No confusion - historical versions don't include future changes</li>
    </ul>
  `;

  return (
    <div className="h-screen flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <h1 className="text-xl font-semibold text-gray-900">
            Hybrid Change Tracking Test
          </h1>
          <div className="text-sm text-gray-600">
            Test the new version control features
          </div>
        </div>
      </div>

      {/* Toolbar */}
      <div className="bg-white border-b border-gray-200 p-2">
        <Toolbar />
      </div>

      {/* Editor */}
      <div className="flex-1">
        <Editor 
          initialContent={initialContent}
          documentId="test-hybrid-tracking"
        />
      </div>
    </div>
  );
}
